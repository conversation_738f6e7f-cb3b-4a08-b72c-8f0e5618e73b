# System Architecture

## Service design and communication patterns

I Took the basics of domain design by making a basic event storming process under the requirements, that can be noted by docs ![Initial Event Storm Perspective](docs/InitialEventStormPerspective.png), ![Initial DDD Macro Venn Perspective](docs/InitialDDDMacroVenn.png), ![Initial DDD Track causes Perspective](docs/InitialDDDTrackCauses.png).

The main idea to accomplish the basics of product scale and cloud first requirements is to use a conteinerized service backed by Kubernetes (with EKS), that provides a good horizontal scale of the containerized services.
The idea is that this service can be fully deployed on the AWS services by the integration of regionalized API gateways, to provide a low latency API distribution by region, along with detached serverless authentication services such as cognito.

The tRPC API is intended to provide a realtime integration between back and front-end allowing for real time UI updates according to the back-end detected events.

Important to mention that Cloudfront has an important role here to provide edge data distribution between the remote stations as well.

 ![Cloud Structure](docs/CloudStructure.png)

## Data storage and real-time updates

The idea of using a simple relational data base such as Postgress, provided through AWS RDS seems to fullfill the main APP relational data storage requirements, along with some support S3 folders for front-end assets and large log datas or even unstructured data run also along some AWS Stepfunction routines and Some agents reporting and detecting actions based on whatever are their routine.

The Agents are also allowed to reach out to front-end by any immediate report with the tRPC API.

## Hardware integration strategy

Since there are different hardwars and even different availability nuances between the Stations, the Possibility of having multiple different AWS API Gateways also allows to have different APIs to handle different communication protocols if needed, thus, also possibilitating to communicate with the same service in different ways.

# Security and user management approach

The usage of Amazon Cognito makes this job much easier since it already provides much of the user pool, authorization and authentication management according with the necessary compliance.

# Failure handling and edge cases

Disaster recovery and other strategies can be driven by this design, since given the exact operational station regions it can be applied AZs or multi region data distribution according to the RTO (Recovery Time Objective) and RPO (Recovery Point Objective).

This design is also entirely rebuildable on other AWS accounts as well with the proper RDS and S3 Backups.

Important to mention that Cognito is compliant with most of the disaster recovery, backup strategies that can be applied along the remaining infrastructure.

## DEV Stack

- [Next.js](https://nextjs.org)
- [Prisma](https://prisma.io)
- [Tailwind CSS](https://tailwindcss.com)
- [tRPC](https://trpc.io)
