import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

export const chargingRouter = createTRPCRouter({
  startSession: publicProcedure
    .input(z.object({ 
      userId: z.number(), 
      stationId: z.number() 
    }))
    .mutation(async ({ ctx, input }) => {
      // Check user balance
      const user = await ctx.db.user.findUnique({
        where: { id: input.userId }
      });
      
      if (!user || (user.balance ?? 0) < 5) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Insufficient balance. Minimum $5 required."
        });
      }

      // Check station availability
      const station = await ctx.db.station.findUnique({
        where: { id: input.stationId }
      });

      if (!station || station.status !== "ACTIVE" || station.availableDocks <= 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Station not available for charging."
        });
      }

      // Create charging session
      const session = await ctx.db.chargingSession.create({
        data: {
          userId: input.userId,
          stationId: input.stationId,
        },
        include: {
          user: true,
          station: true
        }
      });

      // Update station availability
      await ctx.db.station.update({
        where: { id: input.stationId },
        data: {
          availableDocks: { decrement: 1 },
          busyDocks: { increment: 1 }
        }
      });

      return session;
    }),

  endSession: publicProcedure
    .input(z.object({ 
      sessionId: z.number(),
      amount: z.number().min(0)
    }))
    .mutation(async ({ ctx, input }) => {
      const session = await ctx.db.chargingSession.findUnique({
        where: { id: input.sessionId },
        include: { station: true, user: true }
      });

      if (!session) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Charging session not found."
        });
      }

      // Update session
      const updatedSession = await ctx.db.chargingSession.update({
        where: { id: input.sessionId },
        data: {
          endTime: new Date(),
          amount: input.amount,
          status: "COMPLETED"
        },
        include: {
          user: true,
          station: true
        }
      });

      // Deduct from user balance
      await ctx.db.user.update({
        where: { id: session.userId },
        data: { balance: { decrement: input.amount } }
      });

      // Update station availability
      await ctx.db.station.update({
        where: { id: session.stationId },
        data: {
          availableDocks: { increment: 1 },
          busyDocks: { decrement: 1 }
        }
      });

      return updatedSession;
    }),

  getActiveSessions: publicProcedure
    .input(z.object({ userId: z.number() }))
    .query(async ({ ctx, input }) => {
      return ctx.db.chargingSession.findMany({
        where: { 
          userId: input.userId,
          status: "ACTIVE"
        },
        include: { station: true }
      });
    }),
});