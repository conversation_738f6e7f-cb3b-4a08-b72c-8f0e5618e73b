import { z } from "zod";
import { createTR<PERSON><PERSON>outer, publicProcedure } from "~/server/api/trpc";

export const userRouter = createTRPCRouter({
  getAll: publicProcedure.query(async ({ ctx }) => {
    return ctx.db.user.findMany({
      include: {
        ownedStations: true,
        chargingSessions: {
          include: { station: true },
          orderBy: { createdAt: "desc" }
        }
      },
    });
  }),

  getById: publicProcedure
    .input(z.object({ id: z.number() }))
    .query(async ({ ctx, input }) => {
      return ctx.db.user.findUnique({
        where: { id: input.id },
        include: {
          ownedStations: true,
          chargingSessions: {
            include: { station: true },
            orderBy: { createdAt: "desc" }
          }
        }
      });
    }),

  addCredits: publicProcedure
    .input(z.object({ 
      id: z.number(), 
      amount: z.number().min(0.01) 
    }))
    .mutation(async ({ ctx, input }) => {
      return ctx.db.user.update({
        where: { id: input.id },
        data: { 
          balance: { increment: input.amount }
        },
      });
    }),
});