import { z } from "zod";
import { createTR<PERSON><PERSON>outer, publicProcedure } from "~/server/api/trpc";

export const stationRouter = createTRPCRouter({
  getAll: publicProcedure.query(async ({ ctx }) => {
    return ctx.db.station.findMany({
      include: {
        owner: true,
        _count: {
          select: { chargingSessions: { where: { status: "ACTIVE" } } }
        }
      },
    });
  }),

  getById: publicProcedure
    .input(z.object({ id: z.number() }))
    .query(async ({ ctx, input }) => {
      return ctx.db.station.findUnique({
        where: { id: input.id },
        include: { owner: true }
      });
    }),

  updatePrice: publicProcedure
    .input(z.object({ 
      id: z.number(), 
      price: z.number().min(0) 
    }))
    .mutation(async ({ ctx, input }) => {
      return ctx.db.station.update({
        where: { id: input.id },
        data: { price: input.price },
      });
    }),

  updateStatus: publicProcedure
    .input(z.object({ 
      id: z.number(), 
      status: z.enum(["ACTIVE", "OFFLINE", "MAINTENANCE"]) 
    }))
    .mutation(async ({ ctx, input }) => {
      return ctx.db.station.update({
        where: { id: input.id },
        data: { status: input.status },
      });
    }),
});