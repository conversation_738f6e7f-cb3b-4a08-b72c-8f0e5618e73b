import { postRouter } from "~/server/api/routers/post";
import { stationRouter } from "~/server/api/routers/station";
import { userRouter } from "~/server/api/routers/user";
import { chargingRouter } from "~/server/api/routers/charging";
import { createCallerFactory, createTRPCRouter } from "~/server/api/trpc";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  post: postRouter,
  station: stationRouter,
  user: userRouter,
  charging: chargingRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
