"use client";

import { useState } from "react";
import { api } from "~/trpc/react";

interface UserDashboardProps {
  selectedUserId: number | null;
}

export function UserDashboard({ selectedUserId }: UserDashboardProps) {
  const { data: user, refetch } = api.user.getById.useQuery(
    { id: selectedUserId! },
    { enabled: !!selectedUserId }
  );
  
  const addCreditsMutation = api.user.addCredits.useMutation({
    onSuccess: () => {
      refetch();
    }
  });

  const [creditAmount, setCreditAmount] = useState("");

  if (!selectedUserId || !user) return null;

  const handleAddCredits = async () => {
    try {
      await addCreditsMutation.mutateAsync({
        id: selectedUserId,
        amount: parseFloat(creditAmount)
      });
      setCreditAmount("");
      alert("Credits added successfully!");
    } catch (error) {
      alert("Failed to add credits");
    }
  };

  return (
    <div className="mb-6 rounded-lg bg-white/10 p-4">
      <h2 className="mb-4 text-xl font-bold">
        Welcome, {user.firstName} {user.lastName}
      </h2>
      
      <div className="grid gap-4 sm:grid-cols-2">
        <div>
          <p className="text-sm opacity-75">Role: {user.role}</p>
          {user.role === "DRIVER" && (
            <p className="text-lg font-semibold">
              Balance: ${user.balance?.toFixed(2) ?? "0.00"}
            </p>
          )}
        </div>
        
        {user.role === "DRIVER" && (
          <div className="flex items-center gap-2">
            <input
              type="number"
              step="0.01"
              placeholder="Add credits"
              value={creditAmount}
              onChange={(e) => setCreditAmount(e.target.value)}
              className="flex-1 rounded bg-white/20 px-3 py-2 text-white placeholder-white/50"
            />
            <button
              onClick={handleAddCredits}
              disabled={!creditAmount || addCreditsMutation.isPending}
              className="rounded bg-green-600 px-4 py-2 hover:bg-green-700 disabled:opacity-50"
            >
              Add Credits
            </button>
          </div>
        )}
      </div>

      {user.role === "OWNER" && user.ownedStations.length > 0 && (
        <div className="mt-4">
          <h3 className="font-semibold">Your Stations:</h3>
          <div className="mt-2 flex flex-wrap gap-2">
            {user.ownedStations.map((station) => (
              <span key={station.id} className="rounded bg-blue-600 px-2 py-1 text-sm">
                {station.name}
              </span>
            ))}
          </div>
        </div>
      )}

      {user.chargingSessions.length > 0 && (
        <div className="mt-4">
          <h3 className="font-semibold">Recent Sessions:</h3>
          <div className="mt-2 space-y-1 text-sm">
            {user.chargingSessions.slice(0, 3).map((session) => (
              <div key={session.id} className="flex justify-between">
                <span>{session.station.name}</span>
                <span>{session.status} - ${session.amount?.toFixed(2) ?? "Active"}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}