"use client";

import { useState } from "react";
import { api } from "~/trpc/react";

interface StationListProps {
  selectedUserId: number | null;
}

export function StationList({ selectedUserId }: StationListProps) {
  const { data: stations, isLoading, refetch } = api.station.getAll.useQuery();
  const { data: user } = api.user.getById.useQuery(
    { id: selectedUserId! },
    { enabled: !!selectedUserId }
  );
  
  const startChargingMutation = api.charging.startSession.useMutation({
    onSuccess: () => {
      refetch();
    }
  });

  const updatePriceMutation = api.station.updatePrice.useMutation({
    onSuccess: () => {
      refetch();
    }
  });

  const [editingPrice, setEditingPrice] = useState<number | null>(null);
  const [newPrice, setNewPrice] = useState("");

  if (isLoading) return <div>Loading stations...</div>;
  if (!selectedUserId) return <div>Please select a user to continue</div>;

  const handleStartCharging = async (stationId: number) => {
    try {
      await startChargingMutation.mutateAsync({
        userId: selectedUserId,
        stationId
      });
      alert("Charging session started!");
    } catch (error) {
      alert(error instanceof Error ? error.message : "Failed to start charging");
    }
  };

  const handleUpdatePrice = async (stationId: number) => {
    try {
      await updatePriceMutation.mutateAsync({
        id: stationId,
        price: parseFloat(newPrice)
      });
      setEditingPrice(null);
      setNewPrice("");
    } catch (error) {
      alert("Failed to update price");
    }
  };

  const canManageStation = (station: any) => {
    return user?.role === "ADMIN" || 
           (user?.role === "OWNER" && station.ownerId === selectedUserId);
  };

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold">Charging Stations</h2>
      <div className="grid gap-4">
        {stations?.map((station) => (
          <div key={station.id} className="rounded-lg bg-white/10 p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h3 className="text-lg font-semibold">{station.name}</h3>
                <div className="mt-2 grid grid-cols-2 gap-4 text-sm">
                  <div>Power: {station.power}kW</div>
                  <div>Type: {station.chargingType}</div>
                  <div className="flex items-center gap-2">
                    Price: $
                    {editingPrice === station.id ? (
                      <div className="flex items-center gap-2">
                        <input
                          type="number"
                          step="0.01"
                          value={newPrice}
                          onChange={(e) => setNewPrice(e.target.value)}
                          className="w-20 rounded bg-white/20 px-2 py-1 text-white"
                        />
                        <button
                          onClick={() => handleUpdatePrice(station.id)}
                          className="rounded bg-green-600 px-2 py-1 text-xs"
                        >
                          Save
                        </button>
                        <button
                          onClick={() => setEditingPrice(null)}
                          className="rounded bg-gray-600 px-2 py-1 text-xs"
                        >
                          Cancel
                        </button>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        {station.price}/kWh
                        {canManageStation(station) && (
                          <button
                            onClick={() => {
                              setEditingPrice(station.id);
                              setNewPrice(station.price.toString());
                            }}
                            className="rounded bg-blue-600 px-2 py-1 text-xs"
                          >
                            Edit
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                  <div>Hours: {station.startTime} - {station.endTime}</div>
                </div>
                <div className="mt-2 flex items-center gap-4">
                  <span className={`rounded px-2 py-1 text-xs ${
                    station.status === "ACTIVE" ? "bg-green-600" : 
                    station.status === "OFFLINE" ? "bg-red-600" : "bg-yellow-600"
                  }`}>
                    {station.status}
                  </span>
                  <span className="text-sm">
                    Available: {station.availableDocks} | Busy: {station.busyDocks}
                  </span>
                </div>
              </div>
              
              {user?.role === "DRIVER" && station.status === "ACTIVE" && station.availableDocks > 0 && (
                <button
                  onClick={() => handleStartCharging(station.id)}
                  disabled={startChargingMutation.isPending}
                  className="rounded bg-green-600 px-4 py-2 font-medium hover:bg-green-700 disabled:opacity-50"
                >
                  {startChargingMutation.isPending ? "Starting..." : "Start Charging"}
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}