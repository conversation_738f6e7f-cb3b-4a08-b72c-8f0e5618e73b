"use client";

import { api } from "~/trpc/react";

interface UserSelectorProps {
  selectedUserId: number | null;
  onUserSelect: (userId: number) => void;
}

export function UserSelector({ selectedUserId, onUserSelect }: UserSelectorProps) {
  const { data: users, isLoading } = api.user.getAll.useQuery();

  if (isLoading) return <div>Loading users...</div>;

  return (
    <div className="mb-6 rounded-lg bg-white/10 p-4">
      <h3 className="mb-3 text-lg font-semibold">Select User Role</h3>
      <div className="grid grid-cols-1 gap-2 sm:grid-cols-3">
        {users?.map((user) => (
          <button
            key={user.id}
            onClick={() => onUserSelect(user.id)}
            className={`rounded-lg p-3 text-left transition-colors ${
              selectedUserId === user.id
                ? "bg-blue-600 text-white"
                : "bg-white/20 hover:bg-white/30"
            }`}
          >
            <div className="font-medium">{user.firstName} {user.lastName}</div>
            <div className="text-sm opacity-75">{user.role}</div>
            {user.role === "DRIVER" && (
              <div className="text-sm">Balance: ${user.balance?.toFixed(2) ?? "0.00"}</div>
            )}
          </button>
        ))}
      </div>
    </div>
  );
}