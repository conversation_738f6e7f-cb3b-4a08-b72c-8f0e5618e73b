"use client";

import { useState } from "react";
import { api } from "~/trpc/react";

interface ChargingSessionsProps {
  selectedUserId: number | null;
}

export function ChargingSessions({ selectedUserId }: ChargingSessionsProps) {
  const { data: sessions, refetch } = api.charging.getActiveSessions.useQuery(
    { userId: selectedUserId! },
    { enabled: !!selectedUserId }
  );
  
  const endSessionMutation = api.charging.endSession.useMutation({
    onSuccess: () => {
      refetch();
    }
  });

  const [endingSession, setEndingSession] = useState<number | null>(null);
  const [chargeAmount, setChargeAmount] = useState("");

  const handleEndSession = async (sessionId: number) => {
    try {
      await endSessionMutation.mutateAsync({
        sessionId,
        amount: parseFloat(chargeAmount)
      });
      setEndingSession(null);
      setChargeAmount("");
      alert("Charging session completed!");
    } catch (error) {
      alert("Failed to end session");
    }
  };

  if (!selectedUserId || !sessions?.length) {
    return null;
  }

  return (
    <div className="mt-6 space-y-4">
      <h2 className="text-2xl font-bold">Active Charging Sessions</h2>
      <div className="grid gap-4">
        {sessions.map((session) => (
          <div key={session.id} className="rounded-lg bg-yellow-600/20 p-4">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="font-semibold">{session.station.name}</h3>
                <p className="text-sm opacity-75">
                  Started: {new Date(session.startTime).toLocaleString()}
                </p>
                <p className="text-sm">
                  Rate: ${session.station.price}/kWh
                </p>
              </div>
              
              {endingSession === session.id ? (
                <div className="flex items-center gap-2">
                  <input
                    type="number"
                    step="0.01"
                    placeholder="Amount ($)"
                    value={chargeAmount}
                    onChange={(e) => setChargeAmount(e.target.value)}
                    className="w-24 rounded bg-white/20 px-2 py-1 text-white placeholder-white/50"
                  />
                  <button
                    onClick={() => handleEndSession(session.id)}
                    disabled={!chargeAmount || endSessionMutation.isPending}
                    className="rounded bg-red-600 px-3 py-1 text-sm hover:bg-red-700 disabled:opacity-50"
                  >
                    End
                  </button>
                  <button
                    onClick={() => setEndingSession(null)}
                    className="rounded bg-gray-600 px-3 py-1 text-sm"
                  >
                    Cancel
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setEndingSession(session.id)}
                  className="rounded bg-red-600 px-4 py-2 text-sm hover:bg-red-700"
                >
                  Stop Charging
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}