"use client";

import { api } from "~/trpc/react";

interface AdminPanelProps {
  selectedUserId: number | null;
}

export function AdminPanel({ selectedUserId }: AdminPanelProps) {
  const { data: user } = api.user.getById.useQuery(
    { id: selectedUserId! },
    { enabled: !!selectedUserId }
  );
  
  const { data: stations, refetch } = api.station.getAll.useQuery();
  const { data: users } = api.user.getAll.useQuery();
  
  const updateStatusMutation = api.station.updateStatus.useMutation({
    onSuccess: () => {
      refetch();
    }
  });

  if (!selectedUserId || user?.role !== "ADMIN") return null;

  const handleStatusChange = async (stationId: number, status: "ACTIVE" | "OFFLINE" | "MAINTENANCE") => {
    try {
      await updateStatusMutation.mutateAsync({ id: stationId, status });
    } catch (error) {
      alert("Failed to update station status");
    }
  };

  const totalRevenue = users?.reduce((sum, user) => {
    return sum + (user.chargingSessions?.reduce((sessionSum, session) => {
      return sessionSum + (session.amount || 0);
    }, 0) || 0);
  }, 0) || 0;

  const activeStations = stations?.filter(s => s.status === "ACTIVE").length || 0;
  const totalSessions = users?.reduce((sum, user) => sum + (user.chargingSessions?.length || 0), 0) || 0;

  return (
    <div className="mt-6 space-y-6">
      <h2 className="text-2xl font-bold">Admin Dashboard</h2>
      
      {/* Network Overview */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
        <div className="rounded-lg bg-green-600/20 p-4">
          <h3 className="font-semibold">Active Stations</h3>
          <p className="text-2xl font-bold">{activeStations}</p>
        </div>
        <div className="rounded-lg bg-blue-600/20 p-4">
          <h3 className="font-semibold">Total Sessions</h3>
          <p className="text-2xl font-bold">{totalSessions}</p>
        </div>
        <div className="rounded-lg bg-yellow-600/20 p-4">
          <h3 className="font-semibold">Network Revenue</h3>
          <p className="text-2xl font-bold">${totalRevenue.toFixed(2)}</p>
        </div>
      </div>

      {/* Station Management */}
      <div>
        <h3 className="mb-4 text-xl font-semibold">Station Management</h3>
        <div className="space-y-3">
          {stations?.map((station) => (
            <div key={station.id} className="flex items-center justify-between rounded-lg bg-white/10 p-4">
              <div>
                <h4 className="font-medium">{station.name}</h4>
                <p className="text-sm opacity-75">
                  {station.power}kW • ${station.price}/kWh • {station.availableDocks} available
                </p>
              </div>
              <div className="flex items-center gap-2">
                <select
                  value={station.status}
                  onChange={(e) => handleStatusChange(station.id, e.target.value as any)}
                  className="rounded bg-white/20 px-3 py-1 text-white"
                >
                  <option value="ACTIVE">Active</option>
                  <option value="OFFLINE">Offline</option>
                  <option value="MAINTENANCE">Maintenance</option>
                </select>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* User Overview */}
      <div>
        <h3 className="mb-4 text-xl font-semibold">User Overview</h3>
        <div className="space-y-2">
          {users?.map((user) => (
            <div key={user.id} className="flex items-center justify-between rounded-lg bg-white/5 p-3">
              <div>
                <span className="font-medium">{user.firstName} {user.lastName}</span>
                <span className="ml-2 text-sm opacity-75">({user.role})</span>
              </div>
              <div className="text-sm">
                {user.role === "DRIVER" && `Balance: $${user.balance?.toFixed(2) || "0.00"}`}
                {user.role === "OWNER" && `Stations: ${user.ownedStations?.length || 0}`}
                {user.role === "ADMIN" && "System Admin"}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}