"use client";

import { useState } from "react";
import { UserSelector } from "~/app/_components/user-selector";
import { UserDashboard } from "~/app/_components/user-dashboard";
import { StationList } from "~/app/_components/station-list";
import { ChargingSessions } from "~/app/_components/charging-sessions";
import { AdminPanel } from "~/app/_components/admin-panel";

export default function Home() {
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);

  return (
    <main className="min-h-screen bg-gradient-to-b from-[#1e3a8a] to-[#1e1b4b] text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-extrabold tracking-tight sm:text-6xl">
            ⚡ <span className="text-yellow-400">Super EV</span>
          </h1>
          <p className="mt-4 text-xl text-gray-300">
            EV Charging Platform - Proof of Concept
          </p>
        </div>

        <UserSelector 
          selectedUserId={selectedUserId} 
          onUserSelect={setSelectedUserId} 
        />
        
        {selectedUserId && (
          <>
            <UserDashboard selectedUserId={selectedUserId} />
            <StationList selectedUserId={selectedUserId} />
            <ChargingSessions selectedUserId={selectedUserId} />
            <AdminPanel selectedUserId={selectedUserId} />
          </>
        )}
      </div>
    </main>
  );
}
