# ⚡ Super EV - EV Charging Platform POC

A proof-of-concept for an EV charging platform that connects drivers with charging stations and helps station owners manage their business.

## 🚀 Quick Start

See [SETUP.md](./SETUP.md) for detailed setup instructions.

```bash
# Install dependencies
npm install

# Setup database
npm run db:push
npm run db:seed

# Start development server
npm run dev
```

## 🎯 Features Demonstrated

### Core User Workflows
- **Driver Experience**: Find stations, check prices, start charging sessions
- **Station Owner**: Set prices, manage stations, track revenue
- **Admin Dashboard**: Monitor network, handle station status

### Business Rules Implementation
- ✅ Minimum balance validation before charging
- ✅ Station availability checking
- ✅ Operating hours enforcement
- ✅ Real-time status updates
- ✅ Credit/balance management

### Technical Features
- 🔄 Real-time data with tRPC
- 📱 Responsive web interface
- 🗄️ PostgreSQL with Prisma ORM
- 🎨 Modern UI with Tailwind CSS
- 🔒 Type-safe APIs

## 🏗️ Architecture

### Tech Stack
- **Frontend**: Next.js 15 + React 19
- **Backend**: tRPC for type-safe APIs
- **Database**: PostgreSQL + Prisma
- **Styling**: Tailwind CSS
- **Type Safety**: TypeScript throughout

### Data Model
- **Users**: Drivers, Station Owners, Admins
- **Stations**: Location, pricing, availability, status
- **Charging Sessions**: Active sessions with billing

## 🎮 Demo Scenarios

### As a Driver (Jane Smith)
1. Check balance ($10.50)
2. Browse available stations
3. Start charging at Electropole
4. Add credits to account
5. End charging session

### As a Station Owner (Alice Johnson)
1. View owned stations (Electropole, VoltHub)
2. Update pricing for stations
3. Monitor station utilization
4. Track revenue from sessions

### As an Admin (John Doe)
1. Monitor entire network
2. Change station status (Active/Offline/Maintenance)
3. View system-wide metrics
4. Manage user accounts

## 🔧 Development

```bash
# Database operations
npm run db:studio     # Open Prisma Studio
npm run db:push       # Push schema changes
npm run db:seed       # Seed sample data

# Development
npm run dev           # Start dev server
npm run build         # Build for production
npm run lint          # Run linting
```

## 📊 Sample Data

### Users
- **John Doe** (Admin): System administrator
- **Jane Smith** (Driver): $10.50 balance
- **Alice Johnson** (Owner): Owns 2 stations

### Stations
- **Electropole**: 150kW, $0.29/kWh, NYC
- **VoltHub**: 250kW, $0.32/kWh, London
- **GreenCharge**: 50kW, $0.25/kWh, London (Offline)

## 🎯 Business Impact

### For Drivers
- Easy station discovery
- Transparent pricing
- Reliable availability info
- Simple payment system

### For Station Owners
- Dynamic pricing control
- Revenue tracking
- Utilization monitoring
- Operational management

### For Platform
- Scalable architecture
- Real-time monitoring
- Multi-stakeholder support
- Growth-ready foundation
---

**Built with the T3 Stack**: Next.js, tRPC, Prisma, Tailwind CSS