# Super EV - Setup Instructions

## Prerequisites

- Node.js 18+ 
- PostgreSQL database
- npm or yarn

## Database Setup

1. **Start PostgreSQL** (if using Docker):
   ```bash
   ./start-database.sh
   ```

2. **Configure environment variables**:
   Copy `.env.example` to `.env` and update the DATABASE_URL:
   ```
   DATABASE_URL="postgresql://username:password@localhost:5432/super_ev"
   ```

## Installation & Setup

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Generate Prisma client**:
   ```bash
   npm run postinstall
   ```

3. **Run database migrations**:
   ```bash
   npm run db:push
   ```

4. **Seed the database with sample data**:
   ```bash
   npm run db:seed
   ```

## Running the Application

1. **Start the development server**:
   ```bash
   npm run dev
   ```

2. **Open your browser** and navigate to `http://localhost:3000`

## Features Demonstrated

### User Roles
- **Driver**: Find stations, start charging, manage balance
- **Station Owner**: Set prices, manage owned stations  
- **Admin**: Monitor all stations and users

### Core Functionality
- Real-time station status updates
- Business rule validation (minimum balance, station availability)
- Charging session management
- Credit/balance management
- Station price management

### Sample Users
- **John Doe** (Admin): Full system access
- **Jane Smith** (Driver): $10.50 balance, can start charging sessions
- **Alice Johnson** (Owner): Owns Electropole and VoltHub stations

## Database Management

- **View database**: `npm run db:studio`
- **Reset database**: `npm run db:push --force-reset`
- **Re-seed data**: `npm run db:seed`

## Architecture Notes

- **Frontend**: Next.js 15 with React 19
- **Backend**: tRPC for type-safe APIs
- **Database**: PostgreSQL with Prisma ORM
- **Styling**: Tailwind CSS
- **Real-time**: tRPC subscriptions for live updates