// EV Charging Platform Schema
// Defines the data model for stations, users, and charging sessions

generator client {
    provider = "prisma-client-js"
}

datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

model User {
    id        Int      @id @default(autoincrement())
    firstName String
    lastName  String
    email     String   @unique
    role      Role
    balance   Float?   @default(0)
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    ownedStations Station[]
    chargingSessions ChargingSession[]

    @@index([email])
}

model Station {
    id            Int      @id @default(autoincrement())
    name          String
    latitude      Float
    longitude     Float
    power         Int
    price         Float
    availableDocks Int
    busyDocks     Int
    status        StationStatus
    chargingType  String
    startTime     String
    endTime       String
    createdAt     DateTime @default(now())
    updatedAt     DateTime @updatedAt

    ownerId       Int?
    owner         User? @relation(fields: [ownerId], references: [id])
    chargingSessions ChargingSession[]

    @@index([status])
}

model ChargingSession {
    id        Int      @id @default(autoincrement())
    userId    Int
    stationId Int
    startTime DateTime @default(now())
    endTime   DateTime?
    amount    Float?
    status    SessionStatus @default(ACTIVE)
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    user      User @relation(fields: [userId], references: [id])
    station   Station @relation(fields: [stationId], references: [id])

    @@index([userId])
    @@index([stationId])
}

enum Role {
    ADMIN
    DRIVER
    OWNER
}

enum StationStatus {
    ACTIVE
    OFFLINE
    MAINTENANCE
}

enum SessionStatus {
    ACTIVE
    COMPLETED
    CANCELLED
}
