import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  // Create users
  const admin = await prisma.user.create({
    data: {
      firstName: "<PERSON>",
      lastName: "<PERSON><PERSON>", 
      email: "<EMAIL>",
      role: "ADMIN"
    }
  });

  const driver = await prisma.user.create({
    data: {
      firstName: "<PERSON>",
      lastName: "<PERSON>",
      email: "<EMAIL>", 
      role: "DRIVER",
      balance: 10.50
    }
  });

  const owner = await prisma.user.create({
    data: {
      firstName: "<PERSON>",
      lastName: "<PERSON>",
      email: "<EMAIL>",
      role: "OWNER"
    }
  });

  // Create stations
  const station1 = await prisma.station.create({
    data: {
      name: "Electropole",
      latitude: 40.7128,
      longitude: -74.0060,
      power: 150,
      price: 0.29,
      availableDocks: 10,
      busyDocks: 5,
      status: "ACTIVE",
      chargingType: "CCS",
      startTime: "08:00",
      endTime: "20:00",
      ownerId: owner.id
    }
  });

  const station2 = await prisma.station.create({
    data: {
      name: "VoltHub", 
      latitude: 51.4874,
      longitude: -0.1432,
      power: 250,
      price: 0.32,
      availableDocks: 8,
      busyDocks: 1,
      status: "ACTIVE",
      chargingType: "CCS",
      startTime: "08:00", 
      endTime: "22:00",
      ownerId: owner.id
    }
  });

  await prisma.station.create({
    data: {
      name: "GreenCharge",
      latitude: 51.4821,
      longitude: -0.0055,
      power: 50,
      price: 0.25,
      availableDocks: 5,
      busyDocks: 3,
      status: "OFFLINE",
      chargingType: "CCS",
      startTime: "04:00",
      endTime: "23:59"
    }
  });

  console.log("Database seeded successfully!");
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });