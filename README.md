# **⚡ EV Charging Platform Challenge**

**Principal Engineer | 24 Hours (Wall Clock)**

## **The Problem**

You're building the foundation for an EV charging platform that connects drivers with charging stations and helps station owners make money.

**Current state:** 50+ charging stations across 3 cities, need to scale to 10,000+ stations nationwide.

**Your job:** Create a proof-of-concept AND system design that demonstrates your approach.

## **Core Requirements**

### **User Needs**

- **Drivers:** Find stations, see prices, start charging sessions
- **Station Owners:** Set prices, manage stations, track revenue
- **Admins:** Monitor network, handle issues

### **Business Rules**

- Drivers need valid payment method with minimum balance before charging
- Drivers can purchase credits to add to their balance
- Stations set their own operating hours
- Station status must be accurate (available/busy/offline)

### **Technical Constraints**

- Each station has different hardware/APIs
- Unreliable internet at station locations
- Need real-time data when possible
- Must work on mobile and web
- Cloud-first architecture preferred

## **What You'll Deliver**

### **1. Working Proof-of-Concept**

- Interface of your choice (Web page, SPA, TUI, etc.)
- User selection to demonstrate different workflows (not auth)
- Station listing with real-time status
- Charging with business rule validation
- Basic station management

### **2. System Architecture**

- Service design and communication patterns
- Data storage and real-time updates
- Hardware integration strategy
- Security and user management approach
- Failure handling and edge cases

### **3. Implementation Plan**

- Build sequence and priorities
- Technology stack rationale
- Team structure requirements
- Risk assessment and mitigation
- Known Unknowns / unanswered questions

## **Key Challenges**

- **Scale:** Distributed system handling increasing load and data
- **Integration:** Unknown future device protocols and APIs
- **Reliability:** 99.9% uptime with unreliable station connectivity
- **Stakeholders:** Balance driver simplicity with owner control
- **Growth:** Multi-city expansion with varying regulations

## **Evaluation Focus**

**System Thinking:** Balance current needs with future growth, handle ambiguity
**Engineering:** Realistic technology choices, buildable architecture
**Business Impact:** Technical decisions that drive business success

## **Time & Format**

**24 hours** - Think, plan, build focused POC, document your approach - treat it like a work day (or two).

**Deliverables:**

- Working POC with setup instructions
- Architecture and implementation documentation
- Present however works best for you

---

**Goal:** Show how you think about complex systems and turn business problems into technical solutions.

Take your time to think through the problem. Take breaks, have dinner, rest. Don't treat this like a death march. Take the 24 hours and use it how it suits you best. Get done what you can and be prepared to discuss the rest. Use the time to deliver your best work.

**Ready to build the future of EV charging?**
